/* eslint-disable no-undef */
export const CONFIG = {
    API_BASE_URL: API_BASE_URL,
    AUTH_COOKIE_NAME: TOKEN_KEY,
    COOKIE_LOGIN: (COOKIE_LOGIN && COOKIE_LOGIN === 'true') ? true : false,
    PUBLIC_URL: `${PUBLIC_URL}`,
    BUILD_ENV: `${BUILD_ENV}`,
    VERSION: `${VERSION}`
}

const isUAEMatrix = () => {
    try {
        if (window.location.origin.toLowerCase().includes('tpmatrix')) {
            return true;
        }
    }
    catch { }
    return false;
}
const IsSourceCustomerWhatsapp = () => {
    try {
        const url = new URL(window.location.href);
        const params = new URLSearchParams(url.search);
        let src = params.get("src");
        if (!src) {
            return false;
        }
        return (['customerwhatsapp', 'customersms'].indexOf(src.toLowerCase())) > -1 ? true : false
    }
    catch {
        return false;
    }


}
const getSVConfigName = () => {
    let configName = 'SVConfig';

    if (CONFIG.BUILD_ENV.toLowerCase() === 'merge') {
        configName = 'SVConfig_merge'
    }
    if (isUAEMatrix()) {
        configName = 'SVConfig-UAE'
    }
    if(IsSourceCustomerWhatsapp())
    {
        configName='SVConfigCustomer'
    }
    return configName;
}

const getEnvironment = () => {
    switch (CONFIG.BUILD_ENV.toLowerCase()) {
        case 'live':
            return 'L';
        case 'merge':
            return 'U';
        case 'qa':
            return 'Q';
        default:
            return 'Q';
    }
}

function getConfig() {
    let config = {};
    try {
        let configName = localStorage.getItem('configName');
        config = localStorage.getItem(configName);
        config = JSON.parse(config);
        config = config.value;
    }
    catch (e) {
        console.log(e)
    }
    config = config || {};
    return config;
}

export const apiConfig = {
    "environment": getEnvironment(),
    "getConfigAPI": {
        "D": 'https://qamatrixcoreapi.policybazaar.com/api/SalesView/GetConfig',
        "Q": 'https://qamatrixcoreapi.policybazaar.com/api/SalesView/GetConfig',
        "U": 'https://matrixcoreapi.policybazaar.com/api/SalesView/GetConfig',
        "L": 'https://matrixcoreapi.policybazaar.com/api/SalesView/GetConfig'
    },
    "MatrixCoreAPI":
    {
        "D": 'https://qamatrixcoreapi.policybazaar.com/',
        "Q": 'https://qamatrixcoreapi.policybazaar.com/',
        "U": 'https://matrixcoreapi.policybazaar.com/',
        "L": 'https://matrixcoreapi.policybazaar.com/'
    }
};

export let SV_CONFIG = getConfig();


export const NODE_ENV = CONFIG.BUILD_ENV;
export const ConfigUrl = apiConfig["getConfigAPI"][apiConfig["environment"]] + `?Key=${getSVConfigName()}`;
export const MatrixCoreAPI = apiConfig["MatrixCoreAPI"][apiConfig["environment"]];